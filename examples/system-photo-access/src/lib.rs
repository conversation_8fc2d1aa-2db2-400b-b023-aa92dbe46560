//! # 系统相册访问库
//!
//! 这个库提供了通过 swift-bridge 访问 iOS/macOS 系统相册的功能。
//!
//! ## 功能特性
//!
//! - 相册访问权限管理
//! - 相册内容读取
//! - 图片数据获取
//! - 错误处理和用户引导
//!
//! ## 使用示例
//!
//! ```rust
//! use system_photo_access::*;
//!
//! #[tokio::main]
//! async fn main() -> Result<(), PhotoError> {
//!     // 检查权限状态
//!     let status = check_permission_status()?;
//!
//!     if status != PhotoPermissionStatus::Authorized {
//!         // 请求权限
//!         let new_status = request_permission().await?;
//!         if new_status != PhotoPermissionStatus::Authorized {
//!             return Err(PhotoError::PermissionDenied);
//!         }
//!     }
//!
//!     println!("相册访问权限已获得");
//!     Ok(())
//! }
//! ```

// 模块声明
pub mod error;
pub mod permissions;
pub mod types;

// 测试时使用模拟的 Swift 函数
#[cfg(test)]
pub mod mock_swift;

// 重新导出主要类型和函数
pub use error::{
    init_error_localizer, localize_error, ContextualError, ErrorContext, ErrorLocalizer, Language,
    PhotoError, PhotoResult, SwiftErrorConverter,
};
pub use permissions::{
    check_permission_status, get_permission_description, get_permission_suggestion,
    is_permission_sufficient, open_settings, request_permission, should_request_permission,
    PhotoPermissionStatus,
};
pub use types::{
    DateRange, ImageData, ImageFormat, ImageSize, Location, MediaType, PhotoAsset, PhotoQuery,
    SortField, SortOrder,
};

// swift-bridge 桥接模块
#[swift_bridge::bridge]
mod ffi {
    // 权限状态枚举
    enum PhotoPermissionStatus {
        NotDetermined,
        Restricted,
        Denied,
        Authorized,
        Limited,
    }

    // 媒体类型枚举
    enum MediaType {
        Image,
        Video,
        Audio,
        Unknown,
    }

    // 图片格式枚举
    enum ImageFormat {
        JPEG,
        PNG,
        HEIC,
        GIF,
        TIFF,
        Unknown,
    }

    // 错误类型
    enum PhotoError {
        PermissionDenied,
        PermissionRestricted,
        AssetNotFound,
        NetworkError,
        SystemError,
        UnsupportedFormat,
        OutOfMemory,
        Cancelled,
        Timeout,
        SwiftBridgeError,
        InitializationError,
        ConfigurationError,
    }

    // 图片尺寸结构体
    #[swift_bridge(swift_repr = "struct")]
    struct ImageSize {
        width: u32,
        height: u32,
    }

    // 导出 Rust 函数给 Swift 使用
    extern "Rust" {
        // 权限管理
        fn bridge_check_permission_status() -> PhotoPermissionStatus;

        // 错误处理
        fn bridge_convert_error_from_swift(
            domain: &str,
            code: i32,
            description: &str,
        ) -> PhotoError;

        // 初始化
        fn bridge_init_library() -> bool;

        // 相册访问
        fn bridge_get_photo_assets_count() -> usize;

        // 图片数据访问
        fn bridge_get_thumbnail_data(asset_id: &str, width: u32, height: u32) -> Vec<u8>;
    }

    // 导入 Swift 函数供 Rust 使用
    extern "Swift" {
        // 权限管理
        fn photo_bridge_check_permission_status() -> i32;
        fn photo_bridge_open_settings() -> bool;

        // 权限请求回调类型
        type PermissionCallback = fn(i32);
        fn photo_bridge_request_permission(callback: PermissionCallback);

        // 错误处理相关的 Swift 函数
        fn photo_bridge_get_last_error_domain() -> String;
        fn photo_bridge_get_last_error_code() -> i32;
        fn photo_bridge_get_last_error_description() -> String;
    }
}

// 桥接函数实现
use ffi::{
    MediaType as BridgeMediaType, PhotoError as BridgePhotoError,
    PhotoPermissionStatus as BridgePermissionStatus,
};

/// 桥接函数：检查权限状态
fn bridge_check_permission_status() -> BridgePermissionStatus {
    match check_permission_status() {
        Ok(status) => convert_permission_status_to_bridge(status),
        Err(_) => BridgePermissionStatus::Denied,
    }
}

/// 桥接函数：从 Swift 错误信息转换为 PhotoError
fn bridge_convert_error_from_swift(domain: &str, code: i32, description: &str) -> BridgePhotoError {
    let error = SwiftErrorConverter::from_nserror(domain, code, description);
    convert_error_to_bridge(error)
}

/// 桥接函数：初始化库
fn bridge_init_library() -> bool {
    init();
    true
}

/// 桥接函数：获取资源数量
fn bridge_get_photo_assets_count() -> usize {
    #[cfg(test)]
    {
        return crate::mock_swift::mock_get_asset_count(None);
    }

    #[cfg(not(test))]
    {
        // 生产模式下的实现
        0
    }
}

/// 桥接函数：获取缩略图数据
fn bridge_get_thumbnail_data(asset_id: &str, width: u32, height: u32) -> Vec<u8> {
    tracing::debug!("请求缩略图: {} ({}x{})", asset_id, width, height);

    #[cfg(test)]
    {
        return crate::mock_swift::mock_get_thumbnail_data(asset_id, width, height);
    }

    #[cfg(not(test))]
    {
        // 生产模式下的实现
        Vec::new()
    }
}

// 类型转换函数
fn convert_permission_status_to_bridge(status: PhotoPermissionStatus) -> BridgePermissionStatus {
    match status {
        PhotoPermissionStatus::NotDetermined => BridgePermissionStatus::NotDetermined,
        PhotoPermissionStatus::Restricted => BridgePermissionStatus::Restricted,
        PhotoPermissionStatus::Denied => BridgePermissionStatus::Denied,
        PhotoPermissionStatus::Authorized => BridgePermissionStatus::Authorized,
        PhotoPermissionStatus::Limited => BridgePermissionStatus::Limited,
    }
}

fn convert_media_type_from_bridge(media_type: BridgeMediaType) -> MediaType {
    match media_type {
        BridgeMediaType::Image => MediaType::Image,
        BridgeMediaType::Video => MediaType::Video,
        BridgeMediaType::Audio => MediaType::Audio,
        BridgeMediaType::Unknown => MediaType::Unknown,
    }
}

fn convert_error_to_bridge(error: PhotoError) -> BridgePhotoError {
    match error {
        PhotoError::PermissionDenied => BridgePhotoError::PermissionDenied,
        PhotoError::PermissionRestricted => BridgePhotoError::PermissionRestricted,
        PhotoError::AssetNotFound { .. } => BridgePhotoError::AssetNotFound,
        PhotoError::NetworkError { .. } => BridgePhotoError::NetworkError,
        PhotoError::SystemError { .. } => BridgePhotoError::SystemError,
        PhotoError::UnsupportedFormat { .. } => BridgePhotoError::UnsupportedFormat,
        PhotoError::OutOfMemory => BridgePhotoError::OutOfMemory,
        PhotoError::Cancelled => BridgePhotoError::Cancelled,
        PhotoError::Timeout => BridgePhotoError::Timeout,
        PhotoError::SwiftBridgeError { .. } => BridgePhotoError::SwiftBridgeError,
        PhotoError::InitializationError { .. } => BridgePhotoError::InitializationError,
        PhotoError::ConfigurationError { .. } => BridgePhotoError::ConfigurationError,
    }
}

// 版本信息
pub const VERSION: &str = env!("CARGO_PKG_VERSION");

/// 初始化库
///
/// 这个函数应该在使用库的其他功能之前调用。
/// 它会初始化日志记录和其他必要的组件。
pub fn init() {
    // 初始化日志记录
    if std::env::var("RUST_LOG").is_err() {
        std::env::set_var("RUST_LOG", "system_photo_access=info");
    }

    // 初始化 tracing
    let _ = tracing_subscriber::fmt::try_init();

    // 初始化错误本地化（默认中文）
    init_error_localizer(Language::Chinese);

    tracing::info!("系统相册访问库已初始化，版本: {}", VERSION);
}

/// 获取库版本信息
pub fn version() -> &'static str {
    VERSION
}

/// 检查库是否已初始化
pub fn is_initialized() -> bool {
    // 简单检查环境变量是否已设置
    std::env::var("RUST_LOG").is_ok()
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_library_initialization() {
        init();
        assert_eq!(VERSION, env!("CARGO_PKG_VERSION"));
        assert!(is_initialized());
    }

    #[test]
    fn test_version() {
        assert!(!version().is_empty());
        assert_eq!(version(), VERSION);
    }

    #[test]
    fn test_image_size_creation() {
        let size = ImageSize {
            width: 100,
            height: 200,
        };
        assert_eq!(size.width, 100);
        assert_eq!(size.height, 200);
    }

    #[test]
    fn test_error_handling() {
        let error = PhotoError::PermissionDenied;
        assert!(error.is_permission_error());
        assert_eq!(error.error_code(), 1001);
    }

    #[test]
    fn test_permission_status() {
        assert!(should_request_permission(
            PhotoPermissionStatus::NotDetermined
        ));
        assert!(is_permission_sufficient(PhotoPermissionStatus::Authorized));
        assert!(!is_permission_sufficient(PhotoPermissionStatus::Denied));
    }

    #[test]
    fn test_error_conversion() {
        let error = PhotoError::PermissionDenied;
        let bridge_error = convert_error_to_bridge(error);
        assert!(matches!(bridge_error, BridgePhotoError::PermissionDenied));
    }

    #[test]
    fn test_permission_status_conversion() {
        let status = PhotoPermissionStatus::Authorized;
        let bridge_status = convert_permission_status_to_bridge(status);
        assert!(matches!(bridge_status, BridgePermissionStatus::Authorized));
    }

    #[test]
    fn test_media_type_conversion() {
        let media_type = convert_media_type_from_bridge(BridgeMediaType::Image);
        assert_eq!(media_type, MediaType::Image);
    }

    #[test]
    fn test_bridge_functions() {
        // 测试桥接函数
        let status = bridge_check_permission_status();
        assert!(matches!(status, BridgePermissionStatus::Authorized));

        let result = bridge_init_library();
        assert!(result);
    }

    #[test]
    fn test_error_localization() {
        init_error_localizer(Language::Chinese);
        let error = PhotoError::PermissionDenied;
        let localized = localize_error(&error);
        assert_eq!(localized, "相册访问权限被拒绝");
    }

    #[tokio::test]
    async fn test_permission_flow() {
        // 测试完整的权限检查流程
        let status = check_permission_status().unwrap();
        assert_eq!(status, PhotoPermissionStatus::Authorized);

        let status = request_permission().await.unwrap();
        assert_eq!(status, PhotoPermissionStatus::Authorized);

        assert!(open_settings());
    }

    #[test]
    fn test_swift_error_conversion() {
        let error =
            SwiftErrorConverter::from_nserror("PHPhotosErrorDomain", -3311, "Permission denied");
        assert_eq!(error, PhotoError::PermissionDenied);

        let bridge_error =
            bridge_convert_error_from_swift("PHPhotosErrorDomain", -3311, "Permission denied");
        assert!(matches!(bridge_error, BridgePhotoError::PermissionDenied));
    }
}
