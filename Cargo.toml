[package]
name = "swift-bridge"
version = "0.1.57"
edition = "2021"
keywords = ["swift", "ffi", "bindings", "ios", "mac"]
description = "Generate FFI bindings for safe interop between <PERSON>ust and Swift."
repository = "https://github.com/chinedufn/swift-bridge"
license = "Apache-2.0/MIT"

[features]
default = []

# Enables bridging of async functions.
async = ["tokio", "once_cell"]

[build-dependencies]
swift-bridge-build = {version = "0.1.57", path = "crates/swift-bridge-build"}

[dependencies]
swift-bridge-macro = {version = "0.1.57", path = "crates/swift-bridge-macro"}

################################################################################
# Optional features used for async function support.
################################################################################
tokio = {optional = true, version = "1", features = ["rt-multi-thread"]}
once_cell = {optional = true, version = "1.9"}

[workspace]
members = [
  "crates/swift-bridge-build",
  "crates/swift-bridge-cli",
  "crates/swift-bridge-ir",
  "crates/swift-bridge-macro",

  "crates/swift-integration-tests",
  "SwiftRustIntegrationTestRunner/integration-test-create-swift-package",
  "SwiftRustIntegrationTestRunner/swift-package-rust-library-fixture",

  "examples/async-functions",
  "examples/codegen-visualizer",
  "examples/multiple-bridge-modules",
  "examples/rust-binary-calls-swift-package",
  "examples/without-a-bridge-module",
]
